// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		6D389DA725B3BCE600D78CF4 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6D389DA625B3BCE600D78CF4 /* AppDelegate.swift */; };
		6D389DAA25B3BCE600D78CF4 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6D389DA825B3BCE600D78CF4 /* Main.storyboard */; };
		6D389DAC25B3BCE600D78CF4 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6D389DAB25B3BCE600D78CF4 /* ViewController.swift */; };
		6D389DAE25B3BCE600D78CF4 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 6D389DAD25B3BCE600D78CF4 /* Assets.xcassets */; };
		6D389DB525B3BCE700D78CF4 /* BetterTDeck for TweetDeck Extension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 6D389DB425B3BCE700D78CF4 /* BetterTDeck for TweetDeck Extension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		6D389DBA25B3BCE700D78CF4 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6D389DB925B3BCE700D78CF4 /* Cocoa.framework */; };
		6D389DBD25B3BCE700D78CF4 /* SafariWebExtensionHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6D389DBC25B3BCE700D78CF4 /* SafariWebExtensionHandler.swift */; };
		6D389DCD25B3BCE700D78CF4 /* manifest.json in Resources */ = {isa = PBXBuildFile; fileRef = 6D389DCA25B3BCE700D78CF4 /* manifest.json */; };
		6D389DCE25B3BCE700D78CF4 /* _locales in Resources */ = {isa = PBXBuildFile; fileRef = 6D389DCB25B3BCE700D78CF4 /* _locales */; };
		6D389DCF25B3BCE700D78CF4 /* build in Resources */ = {isa = PBXBuildFile; fileRef = 6D389DCC25B3BCE700D78CF4 /* build */; };
		6D7D76B028B17C4A00011DA2 /* background.js in Resources */ = {isa = PBXBuildFile; fileRef = 6D7D76AF28B17C4A00011DA2 /* background.js */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		6D389DB625B3BCE700D78CF4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6D389D9A25B3BCE600D78CF4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6D389DB325B3BCE700D78CF4;
			remoteInfo = "Better TweetDeck for Safari Extension";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		6D389DC525B3BCE700D78CF4 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				6D389DB525B3BCE700D78CF4 /* BetterTDeck for TweetDeck Extension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		6D389DA225B3BCE600D78CF4 /* BetterTDeck for TweetDeck.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "BetterTDeck for TweetDeck.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		6D389DA525B3BCE600D78CF4 /* Better_TweetDeck_for_Safari.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Better_TweetDeck_for_Safari.entitlements; sourceTree = "<group>"; };
		6D389DA625B3BCE600D78CF4 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		6D389DA925B3BCE600D78CF4 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		6D389DAB25B3BCE600D78CF4 /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		6D389DAD25B3BCE600D78CF4 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		6D389DAF25B3BCE600D78CF4 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		6D389DB425B3BCE700D78CF4 /* BetterTDeck for TweetDeck Extension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = "BetterTDeck for TweetDeck Extension.appex"; sourceTree = BUILT_PRODUCTS_DIR; };
		6D389DB925B3BCE700D78CF4 /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = System/Library/Frameworks/Cocoa.framework; sourceTree = SDKROOT; };
		6D389DBC25B3BCE700D78CF4 /* SafariWebExtensionHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SafariWebExtensionHandler.swift; sourceTree = "<group>"; };
		6D389DBE25B3BCE700D78CF4 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		6D389DBF25B3BCE700D78CF4 /* Better_TweetDeck_for_Safari_Extension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Better_TweetDeck_for_Safari_Extension.entitlements; sourceTree = "<group>"; };
		6D389DCA25B3BCE700D78CF4 /* manifest.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; name = manifest.json; path = ../../../dist/manifest.json; sourceTree = "<group>"; };
		6D389DCB25B3BCE700D78CF4 /* _locales */ = {isa = PBXFileReference; lastKnownFileType = folder; name = _locales; path = ../../../dist/_locales; sourceTree = "<group>"; };
		6D389DCC25B3BCE700D78CF4 /* build */ = {isa = PBXFileReference; lastKnownFileType = folder; name = build; path = ../../../dist/build; sourceTree = "<group>"; };
		6D7D76AF28B17C4A00011DA2 /* background.js */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.javascript; name = background.js; path = ../../../dist/background.js; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		6D389D9F25B3BCE600D78CF4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6D389DB125B3BCE700D78CF4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6D389DBA25B3BCE700D78CF4 /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		6D389D9925B3BCE600D78CF4 = {
			isa = PBXGroup;
			children = (
				6D389DA425B3BCE600D78CF4 /* Better TweetDeck for Safari */,
				6D389DBB25B3BCE700D78CF4 /* Better TweetDeck for Safari Extension */,
				6D389DB825B3BCE700D78CF4 /* Frameworks */,
				6D389DA325B3BCE600D78CF4 /* Products */,
			);
			sourceTree = "<group>";
		};
		6D389DA325B3BCE600D78CF4 /* Products */ = {
			isa = PBXGroup;
			children = (
				6D389DA225B3BCE600D78CF4 /* BetterTDeck for TweetDeck.app */,
				6D389DB425B3BCE700D78CF4 /* BetterTDeck for TweetDeck Extension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		6D389DA425B3BCE600D78CF4 /* Better TweetDeck for Safari */ = {
			isa = PBXGroup;
			children = (
				6D389DA525B3BCE600D78CF4 /* Better_TweetDeck_for_Safari.entitlements */,
				6D389DA625B3BCE600D78CF4 /* AppDelegate.swift */,
				6D389DA825B3BCE600D78CF4 /* Main.storyboard */,
				6D389DAB25B3BCE600D78CF4 /* ViewController.swift */,
				6D389DAD25B3BCE600D78CF4 /* Assets.xcassets */,
				6D389DAF25B3BCE600D78CF4 /* Info.plist */,
			);
			path = "Better TweetDeck for Safari";
			sourceTree = "<group>";
		};
		6D389DB825B3BCE700D78CF4 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6D389DB925B3BCE700D78CF4 /* Cocoa.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		6D389DBB25B3BCE700D78CF4 /* Better TweetDeck for Safari Extension */ = {
			isa = PBXGroup;
			children = (
				6D389DC925B3BCE700D78CF4 /* Resources */,
				6D389DBC25B3BCE700D78CF4 /* SafariWebExtensionHandler.swift */,
				6D389DBE25B3BCE700D78CF4 /* Info.plist */,
				6D389DBF25B3BCE700D78CF4 /* Better_TweetDeck_for_Safari_Extension.entitlements */,
			);
			path = "Better TweetDeck for Safari Extension";
			sourceTree = "<group>";
		};
		6D389DC925B3BCE700D78CF4 /* Resources */ = {
			isa = PBXGroup;
			children = (
				6D7D76AF28B17C4A00011DA2 /* background.js */,
				6D389DCA25B3BCE700D78CF4 /* manifest.json */,
				6D389DCB25B3BCE700D78CF4 /* _locales */,
				6D389DCC25B3BCE700D78CF4 /* build */,
			);
			name = Resources;
			path = "Better TweetDeck for Safari Extension";
			sourceTree = SOURCE_ROOT;
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		6D389DA125B3BCE600D78CF4 /* BetterTDeck for TweetDeck */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6D389DC625B3BCE700D78CF4 /* Build configuration list for PBXNativeTarget "BetterTDeck for TweetDeck" */;
			buildPhases = (
				6D389D9E25B3BCE600D78CF4 /* Sources */,
				6D389D9F25B3BCE600D78CF4 /* Frameworks */,
				6D389DA025B3BCE600D78CF4 /* Resources */,
				6D389DC525B3BCE700D78CF4 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				6D389DB725B3BCE700D78CF4 /* PBXTargetDependency */,
			);
			name = "BetterTDeck for TweetDeck";
			productName = "Better TweetDeck for Safari";
			productReference = 6D389DA225B3BCE600D78CF4 /* BetterTDeck for TweetDeck.app */;
			productType = "com.apple.product-type.application";
		};
		6D389DB325B3BCE700D78CF4 /* BetterTDeck for TweetDeck Extension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6D389DC225B3BCE700D78CF4 /* Build configuration list for PBXNativeTarget "BetterTDeck for TweetDeck Extension" */;
			buildPhases = (
				6D389DB025B3BCE700D78CF4 /* Sources */,
				6D389DB125B3BCE700D78CF4 /* Frameworks */,
				6D389DB225B3BCE700D78CF4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "BetterTDeck for TweetDeck Extension";
			productName = "Better TweetDeck for Safari Extension";
			productReference = 6D389DB425B3BCE700D78CF4 /* BetterTDeck for TweetDeck Extension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		6D389D9A25B3BCE600D78CF4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1230;
				LastUpgradeCheck = 1410;
				TargetAttributes = {
					6D389DA125B3BCE600D78CF4 = {
						CreatedOnToolsVersion = 12.3;
					};
					6D389DB325B3BCE700D78CF4 = {
						CreatedOnToolsVersion = 12.3;
					};
				};
			};
			buildConfigurationList = 6D389D9D25B3BCE600D78CF4 /* Build configuration list for PBXProject "BetterTDeck for TweetDeck" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 6D389D9925B3BCE600D78CF4;
			productRefGroup = 6D389DA325B3BCE600D78CF4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				6D389DA125B3BCE600D78CF4 /* BetterTDeck for TweetDeck */,
				6D389DB325B3BCE700D78CF4 /* BetterTDeck for TweetDeck Extension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		6D389DA025B3BCE600D78CF4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6D389DAE25B3BCE600D78CF4 /* Assets.xcassets in Resources */,
				6D389DAA25B3BCE600D78CF4 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6D389DB225B3BCE700D78CF4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6D7D76B028B17C4A00011DA2 /* background.js in Resources */,
				6D389DCF25B3BCE700D78CF4 /* build in Resources */,
				6D389DCE25B3BCE700D78CF4 /* _locales in Resources */,
				6D389DCD25B3BCE700D78CF4 /* manifest.json in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6D389D9E25B3BCE600D78CF4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6D389DAC25B3BCE600D78CF4 /* ViewController.swift in Sources */,
				6D389DA725B3BCE600D78CF4 /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6D389DB025B3BCE700D78CF4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6D389DBD25B3BCE700D78CF4 /* SafariWebExtensionHandler.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		6D389DB725B3BCE700D78CF4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6D389DB325B3BCE700D78CF4 /* BetterTDeck for TweetDeck Extension */;
			targetProxy = 6D389DB625B3BCE700D78CF4 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		6D389DA825B3BCE600D78CF4 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				6D389DA925B3BCE600D78CF4 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		6D389DC025B3BCE700D78CF4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		6D389DC125B3BCE700D78CF4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Release;
		};
		6D389DC325B3BCE700D78CF4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = "Better TweetDeck for Safari Extension/Better_TweetDeck_for_Safari_Extension.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = 2DZ6SL2K4W;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = "Better TweetDeck for Safari Extension/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@executable_path/../../../../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = "$(RECOMMENDED_MACOSX_DEPLOYMENT_TARGET)";
				MARKETING_VERSION = 4.11.3;
				PRODUCT_BUNDLE_IDENTIFIER = "me.erambert.bettertweetdeck-safari.extension";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		6D389DC425B3BCE700D78CF4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = "Better TweetDeck for Safari Extension/Better_TweetDeck_for_Safari_Extension.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = 2DZ6SL2K4W;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = "Better TweetDeck for Safari Extension/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@executable_path/../../../../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = "$(RECOMMENDED_MACOSX_DEPLOYMENT_TARGET)";
				MARKETING_VERSION = 4.11.3;
				PRODUCT_BUNDLE_IDENTIFIER = "me.erambert.bettertweetdeck-safari.extension";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		6D389DC725B3BCE700D78CF4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "Better TweetDeck for Safari/Better_TweetDeck_for_Safari.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = 2DZ6SL2K4W;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = "Better TweetDeck for Safari/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = "$(RECOMMENDED_MACOSX_DEPLOYMENT_TARGET)";
				MARKETING_VERSION = 4.11.3;
				PRODUCT_BUNDLE_IDENTIFIER = "me.erambert.bettertweetdeck-safari";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		6D389DC825B3BCE700D78CF4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "Better TweetDeck for Safari/Better_TweetDeck_for_Safari.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = 2DZ6SL2K4W;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = "Better TweetDeck for Safari/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = "$(RECOMMENDED_MACOSX_DEPLOYMENT_TARGET)";
				MARKETING_VERSION = 4.11.3;
				PRODUCT_BUNDLE_IDENTIFIER = "me.erambert.bettertweetdeck-safari";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6D389D9D25B3BCE600D78CF4 /* Build configuration list for PBXProject "BetterTDeck for TweetDeck" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6D389DC025B3BCE700D78CF4 /* Debug */,
				6D389DC125B3BCE700D78CF4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6D389DC225B3BCE700D78CF4 /* Build configuration list for PBXNativeTarget "BetterTDeck for TweetDeck Extension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6D389DC325B3BCE700D78CF4 /* Debug */,
				6D389DC425B3BCE700D78CF4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6D389DC625B3BCE700D78CF4 /* Build configuration list for PBXNativeTarget "BetterTDeck for TweetDeck" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6D389DC725B3BCE700D78CF4 /* Debug */,
				6D389DC825B3BCE700D78CF4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 6D389D9A25B3BCE600D78CF4 /* Project object */;
}
