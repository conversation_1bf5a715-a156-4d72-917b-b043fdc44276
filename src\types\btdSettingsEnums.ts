export enum BetterTweetDeckAccentColors {
  DEFAULT = 'rgb(29, 161, 242)',
  YELLOW = 'rgb(255, 173, 31)',
  RED = 'rgb(224, 36, 94)',
  PINK = 'rgb(224, 36, 142)',
  PURPLE = 'rgb(121, 75, 196)',
  ORANGE = 'rgb(244, 93, 34)',
  GREEN = 'rgb(23, 191, 99)',
  CUSTOM = 'CUSTOM',
}

export enum BetterTweetDeckThemes {
  LIGHT = 'light',
  DARK = 'default',
  LEGACY_DARK = 'legacy',
  ULTRA_DARK = 'ultra_dark',
}

export enum BTDAvatarShapes {
  SQUARE = 'square',
  CIRCLE = 'circle',
}

export enum BTDScrollbarsMode {
  DEFAULT = 'default',
  SLIM = 'slim',
  HIDDEN = 'hidden',
}

export enum BTDTimestampFormats {
  RELATIVE = 'relative',
  CUSTOM = 'custom',
}

export enum BTDTweetActionsPosition {
  LEFT = 'left',
  RIGHT = 'right',
}

export enum BTDLogoVariations {
  DEFAULT = 'btd',
  AGENDER = 'agender',
  ASEXUAL = 'asexual',
  ANDROGYNE = 'androgyne',
  AROMANTIC = 'aromantic',
  BIGENDER = 'bigender',
  BISEXUAL = 'bisexual',
  DEMIGIRL = 'demigirl',
  DEMIGUY = 'demiguy',
  DEMINONBINARY = 'deminonbinary',
  DEMISEXUAL = 'demisexual',
  ENBIAN = 'enbian',
  GENDERFLUID = 'genderfluid',
  GENDERQUEER = 'genderqueer',
  INTERSEX = 'intersex',
  LESBIAN = 'lesbian',
  NEUTROIS = 'neutrois',
  NON_BINARY = 'non binary',
  OMNISEXUAL = 'omnisexual',
  PANSEXUAL = 'pansexual',
  POLYAMORY = 'polyamory',
  POLYSEXUAL = 'polysexual',
  PROGRESS = 'progress',
  RAINBOW = 'rainbow',
  TRANS = 'trans',
}

export enum BTDUsernameFormat {
  /** `Fullname @username` (default) */
  DEFAULT = 'both',
  /** `@username Fullname` */
  USER_FULL = 'inverted',
  /** `@username` */
  USER = 'username',
  /** `Fullname */
  FULL = 'fullname',
}

export enum BTDMutualBadges {
  HEART = 'heart',
  ARROWS = 'arrows',
}

export enum BTDVerifiedBlueBadges {
  BLUE = 'blue',
  DOLLAR = 'dollar',
  CLOWN = 'clown',
  NERD = 'nerd',
  CROOKED = 'crooked',
}
