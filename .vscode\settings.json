{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[css]": {"editor.formatOnSave": true}, "[json]": {"editor.formatOnSave": true, "editor.defaultFormatter": "vscode.json-language-features"}, "[markdown]": {"editor.formatOnSave": true}, "emmet.excludeLanguages": [], "emmet.includeLanguages": {"javascript": "javascriptreact", "markdown": "javascriptreact", "typescript": "typescriptreact"}, "files.eol": "\n", "files.exclude": {"**/.DS_Store": true, "**/.git": true, "**/.hg": true, "**/.svn": true, "**/CVS": true, ".cache-loader": true}, "i18n-ally.enabledFrameworks": ["chrome-ext", "custom"], "i18n-ally.extract.keyPrefix": "settings_", "i18n-ally.keystyle": "nested", "i18n-ally.localesPaths": ["src/_locales"], "i18n-ally.preferredDelimiter": "_", "movets.skipWarning": true, "search.exclude": {"**/bower_components": true, "**/node_modules": true}, "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": false, "typescript.preferences.useAliasesForRenames": false, "typescript.tsdk": "node_modules/typescript/lib"}