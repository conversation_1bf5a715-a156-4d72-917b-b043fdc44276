import './badgesOnTopOfAvatars.css';

import {DateTime} from 'luxon';

import {hasProperty} from '../helpers/typeHelpers';
import {onChirpAdded} from '../services/chirpHandler';
import {makeBTDModule, makeBtdUuidSelector} from '../types/btdCommonTypes';
import {BTDMutualBadges} from '../types/btdSettingsEnums';
import {
  ChirpBaseTypeEnum,
  TweetDeckControllerClient,
  TweetDeckControllerRelationshipResult,
  TweetDeckUser,
  TwitterActionEnum,
} from '../types/tweetdeckTypes';

export const putBadgesOnTopOfAvatars = makeBTDModule(({TD, settings}) => {
  if (!settings.badgesOnTopOfAvatars) {
    return;
  }
  document.body.setAttribute('btd-badges-top-avatar', String(settings.badgesOnTopOfAvatars));

  onChirpAdded(async (addedChirp) => {
    const {chirp, chirpExtra} = addedChirp;
    const actionOrType = chirpExtra.action || chirpExtra.chirpType;
    let userForBadge: TweetDeckUser | undefined;
    const classesToAdd = ['btd-badge'];
    const chirpNode = document.querySelector(makeBtdUuidSelector('data-btd-uuid', addedChirp.uuid));

    if (!chirpNode) {
      return;
    }

    switch (actionOrType) {
      case TwitterActionEnum.RETWEET:
      case TwitterActionEnum.RETWEETED_RETWEET:
      case TwitterActionEnum.RETWEETED_MEDIA:
      case TwitterActionEnum.RETWEETED_MENTION:
      case TwitterActionEnum.FAVORITE:
      case TwitterActionEnum.FAVORITED_MEDIA:
      case TwitterActionEnum.FAVORITED_MENTION:
      case TwitterActionEnum.FAVORITED_RETWEET:
        if (chirpNode.querySelector('.has-source-avatar')) {
          userForBadge = chirp.sourceUser;
          classesToAdd.push('btd-mini-badge');
        } else {
          userForBadge = chirp.targetTweet?.user;
        }

        break;

      case TwitterActionEnum.FOLLOW: {
        userForBadge = (hasProperty(chirp, 'following') && chirp.following) as
          | TweetDeckUser
          | undefined;
        break;
      }

      case TwitterActionEnum.MENTION:
      case TwitterActionEnum.QUOTED_TWEET:
      case TwitterActionEnum.QUOTE:
        userForBadge = chirp.sourceUser;
        break;

      case TwitterActionEnum.LIST_MEMBER_ADDED:
      case TwitterActionEnum.LIST_MEMBER_REMOVED:
        userForBadge = chirp.owner;
        classesToAdd.push('btd-mini-badge');
        break;

      case ChirpBaseTypeEnum.MESSAGE_THREAD:
        if (chirp.participants.length > 1) {
          break;
        }

        userForBadge = chirp.participants[0];
        break;

      case ChirpBaseTypeEnum.TWEET:
        userForBadge = chirp.retweetedStatus ? chirp.retweetedStatus.user : chirp.user;
        break;

      case ChirpBaseTypeEnum.MESSAGE:
        userForBadge = chirp.sender;
        break;

      default:
        break;
    }

    if (!userForBadge || !chirpNode) {
      return;
    }

    if (userForBadge.isVerified && settings.verifiedBadges) {
      chirpNode.classList.add(...classesToAdd, 'btd-verified-badge');
    } else if (userForBadge.isBlueVerified && settings.verifiedBadges) {
      chirpNode.classList.add(...classesToAdd, 'btd-blue-verified-badge');
    } else if (userForBadge.isTranslator && settings.translatorBadges) {
      chirpNode.classList.add(...classesToAdd, 'btd-translator-badge');
    } else if (userForBadge.isBusinessVerified && settings.verifiedBadges) {
      chirpNode.classList.add(...classesToAdd, `btd-verified-business-badge`);
    } else if (userForBadge.isGovernmentVerified && settings.verifiedBadges) {
      chirpNode.classList.add(...classesToAdd, `btd-verified-government-badge`);
    }

    if (userForBadge.following && settings.mutualBadges) {
      const followerStatus = await getFollowerStatus(userForBadge);

      if (followerStatus) {
        if (
          followerStatus.relationship.target.followed_by &&
          followerStatus.relationship.target.following
        ) {
          chirpNode.classList.add(...classesToAdd, 'btd-mutual-badge');
          if (settings.mutualBadgeVariation === BTDMutualBadges.HEART) {
            chirpNode.classList.add('btd-mutual-heart');
          } else {
            chirpNode.classList.add('btd-mutual-arrows');
          }
        }
      }
    }
  });

  type CachedRelationship = TweetDeckControllerRelationshipResult & {
    requested_date: number;
  };

  const relationshipCache = new Map<string, CachedRelationship>();

  async function getRelationForUserAndClient(
    client: TweetDeckControllerClient,
    user: TweetDeckUser
  ): Promise<CachedRelationship | undefined> {
    // Computing a cache key
    const cacheKey = `${client.oauth.account.state.userId}-${user.id}`;
    // Looking into our cache
    const fromCache = relationshipCache.get(cacheKey);
    const now = DateTime.local();

    if (fromCache) {
      const requestDate = DateTime.fromMillis(fromCache.requested_date);
      const diff = requestDate.diff(now, 'hours');

      // If our cache is less than 12 hours old, take from it.
      if (diff.hours <= 12) {
        return relationshipCache.get(cacheKey);
      }
    }

    return new Promise<CachedRelationship>((resolve) => {
      client.showFriendship(client.oauth.account.state.userId, null, user.screenName, (result) => {
        const toCache = {...result, requested_date: Date.now()};
        resolve(toCache);
        relationshipCache.set(cacheKey, toCache);
      });
    });
  }

  async function getFollowerStatus(user: TweetDeckUser) {
    const client = TD.controller.clients.getClient(user.account.privateState.key);

    if (!client) {
      return undefined;
    }

    return getRelationForUserAndClient(client, user);
  }
});
