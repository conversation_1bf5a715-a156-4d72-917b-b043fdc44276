// Based on https://github.com/tusharmath/node-config-ts/blob/master/src/createTypedefs.ts
import * as config from 'config';
import * as fs from 'fs';
import * as path from 'path';
import * as prettier from 'prettier';

const JsonToTS = require('json-to-ts');

const ts = prettier.format(
  [
    `// This file is auto-generated by tools/createDefineConfig.ts`,
    ...JsonToTS(config.get('Client'), {rootName: 'BtdClientConfig'}),
    `declare const __BTD_CONFIG: BtdClientConfig;`,
    `export const BtdConfig = __BTD_CONFIG as BtdClientConfig;`,
  ].join('\n'),
  {
    parser: 'typescript',
  }
);

fs.writeFileSync(path.resolve(process.cwd(), './src/defineConfig.ts'), ts);
