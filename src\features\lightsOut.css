html.dark body[btd-theme='super-dark'],
html.dark body[btd-theme='old-grey'] {
  & .app-content .column-panel,
  & .app-content .column,
  & .app-navigator,
  & .column-nav-item,
  & .app-header,
  & .app-title,
  & .column-options,
  & .column-panel,
  & .column-header,
  & .column-header-temp,
  & .column-background-fill,
  & .stream-item,
  & .column-options .button-tray,
  & .facet-type.is-active,
  & .app-content .stream-item,
  & .app-content .column-message,
  & .app-content .column-header,
  & .app-content .column-header-temp,
  & .btn-on-blue,
  & .detail-view-inline,
  & .prf-meta,
  & .mdl,
  & .popover,
  & .dropdown-menu,
  & .bg-color-twitter-midnight-dark-gray,
  & {
    background-color: var(--btd-theme-background);
  }

  & .detail-view-inline {
    background: var(--btd-theme-background);
    border: none;
  }

  & .btn-on-blue:hover {
    background-color: var(--btd-theme-btn-hover);
  }

  & .compose,
  & .inline-reply,
  & .old-composer-footer,
  & .mdl-column-med,
  & .stream-item.is-selected-tweet {
    background-color: var(--btd-theme-background-lighter);
  }

  & .stream-item.is-unread {
    background-color: #163043;
  }

  & .reply-triangle {
    border-bottom-color: var(--btd-theme-background-lighter);
  }

  & .attach-compose-buttons .Button.tweet-button,
  & .attach-compose-buttons button.tweet-button,
  & .attach-compose-buttons input.tweet-button[type='button'] {
    background-color: var(--btd-theme-background-lighter);
  }

  & .attach-compose-buttons button.tweet-button:hover {
    background-color: var(--btd-theme-background-lighter) !important;
  }

  & .js-hide-drawer.Button.tweet-button,
  & .js-hide-drawer.Button.tweet-button:hover {
    color: white;
    background: var(--btd-theme-background-lighter) !important;
  }
}

html.dark body[btd-theme='super-dark'] {
  --btd-theme-background: black;
  --btd-theme-background-lighter: #0c0c0c;
  --btd-theme-btn-hover: #2b2b2b;

  & .app-content,
  & .app-columns-container {
    background: #080808;
  }
}

html.dark body[btd-theme='old-grey'] {
  --btd-theme-background: #1c1f21;
  --btd-theme-background-lighter: #242b30;
  --btd-theme-btn-hover: #2b2b2b;

  & .app-content,
  & .app-columns-container {
    background: #14171a;
  }

  & .app-content .column-header,
  & .column-nav-item,
  & .app-header,
  & .app-title,
  & .app-navigator,
  & {
    background: var(--btd-theme-background-lighter);
  }
}
