html .app-content {
  /* collapsed column states */

  & section.btd-column-collapsed {
    &.column {
      width: 52px !important;
    }

    & .column-header {
      transform-origin: 0 100%;
      transform: rotate(90deg);
      width: 100vh;
      top: -50px;
      display: flex;
      flex-direction: row;
    }

    & .column-header [data-action='options'] {
      display: none;
    }

    & i.column-type-icon {
      transform-origin: 50% 100%;
      transform: rotate(-90deg);
      position: relative;
      left: 12px;
      bottom: 0;
      order: 1;
    }

    & .column-title {
      order: 2;
      margin-left: 4px;
      flex-grow: 0;
    }

    & .column-header-links {
      margin-left: 16px;
    }

    & .column-header-links {
      width: auto;
      order: 0;
      position: relative;
      margin-left: 10px;
    }

    &.column-type-message .column-header-links {
      flex-direction: row-reverse;
    }

    &.column-type-message .column-header-links li:nth-child(3) {
      order: 1;
    }

    & i.column-type-icon::before {
      position: relative;
      top: -14px;
    }

    /* uiColumnTitleRefreshed forces us to change this class (mustache recalc), or we could cheat */
    & .icon-minus::before {
      content: '\f183';
    }

    & .column-content,
    & .js-add-to-customtimeline {
      display: none;
    }
  }

  & .column-header-links {
    max-width: none !important;
    flex-shrink: 0;
  }

  & .column-header {
    display: flex;
  }

  & .column-header .column-title,
  & .column-header-temp .column-title {
    margin-right: 0 !important;
    flex-grow: 1;
    flex-shrink: 1;
  }

  & .column-header .column-title-editable {
    display: flex;
    align-items: center;
  }

  & .column-header .column-title-editable img {
    display: none;
  }

  & section.js-column.column.column-type-search .column-header-links {
    width: 100%;
  }
}

[btd-clear-all='true'] {
  & .column-navigator {
    bottom: 300px !important;
  }

  & .column-navigator-overflow {
    bottom: 330px !important;
  }
}

.icon-reload::before {
  content: '\F303';
}
