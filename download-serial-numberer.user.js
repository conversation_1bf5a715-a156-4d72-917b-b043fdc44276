// ==UserScript==
// @name         Twitter/X Download Serial Numberer
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Automatically adds serial numbers to downloaded Twitter/X images and videos
// <AUTHOR>
// @match        https://x.com/*
// @match        https://twitter.com/*
// @match        https://pbs.twimg.com/*
// @match        https://video.twimg.com/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // Track downloads per tweet/user to assign serial numbers
    const downloadCounters = new Map();
    
    // Custom filename format - you can modify this
    const FILENAME_FORMAT = '{{postedUser}}-{{fileName}}_{{serial}}-{{month}}-{{day}}-{{year}}-{{hours}}-{{minutes}}.{{fileExtension}}';
    
    // Extract tweet context from current page
    function getTweetContext() {
        const url = window.location.href;
        let tweetId = null;
        let username = null;
        
        // Extract from URL patterns
        if (url.includes('/status/')) {
            const match = url.match(/\/status\/(\d+)/);
            tweetId = match ? match[1] : null;
        }
        
        // Try to get username from URL or page
        const userMatch = url.match(/\/([^\/]+)\/status/) || url.match(/x\.com\/([^\/]+)/);
        if (userMatch) {
            username = userMatch[1];
        }
        
        // Fallback: try to get from page elements
        if (!username) {
            const userElement = document.querySelector('[data-testid="User-Name"] a') || 
                               document.querySelector('[data-screen-name]');
            if (userElement) {
                username = userElement.textContent.replace('@', '') || 
                          userElement.getAttribute('data-screen-name');
            }
        }
        
        return { tweetId, username: username || 'unknown' };
    }
    
    // Generate filename with serial number
    function generateFilename(originalUrl, context) {
        const url = new URL(originalUrl);
        const pathname = url.pathname;
        const fileName = pathname.split('/').pop().split('.')[0];
        const fileExtension = pathname.split('.').pop() || 'jpg';
        
        // Create unique key for this download session
        const contextKey = `${context.username}-${context.tweetId || 'unknown'}`;
        
        // Get and increment serial number
        const currentSerial = downloadCounters.get(contextKey) || 0;
        const serial = currentSerial + 1;
        downloadCounters.set(contextKey, serial);
        
        // Get current date/time
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        
        // Replace template variables
        let filename = FILENAME_FORMAT
            .replace('{{postedUser}}', context.username)
            .replace('{{fileName}}', fileName)
            .replace('{{serial}}', String(serial).padStart(2, '0'))
            .replace('{{month}}', month)
            .replace('{{day}}', day)
            .replace('{{year}}', year)
            .replace('{{hours}}', hours)
            .replace('{{minutes}}', minutes)
            .replace('{{fileExtension}}', fileExtension);
            
        return filename;
    }
    
    // Override the native download function
    function interceptDownloads() {
        // Intercept anchor downloads
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
            const element = originalCreateElement.call(this, tagName);
            
            if (tagName.toLowerCase() === 'a') {
                const originalClick = element.click;
                element.click = function() {
                    if (this.download && this.href) {
                        const context = getTweetContext();
                        if (this.href.includes('pbs.twimg.com') || this.href.includes('video.twimg.com')) {
                            const newFilename = generateFilename(this.href, context);
                            console.log(`[Serial Numberer] Renaming download: ${this.download} → ${newFilename}`);
                            this.download = newFilename;
                        }
                    }
                    return originalClick.call(this);
                };
            }
            
            return element;
        };
        
        // Intercept programmatic downloads
        const originalAppendChild = Node.prototype.appendChild;
        Node.prototype.appendChild = function(child) {
            if (child.tagName === 'A' && child.download && child.href) {
                const context = getTweetContext();
                if (child.href.includes('pbs.twimg.com') || child.href.includes('video.twimg.com')) {
                    const newFilename = generateFilename(child.href, context);
                    console.log(`[Serial Numberer] Renaming download: ${child.download} → ${newFilename}`);
                    child.download = newFilename;
                }
            }
            return originalAppendChild.call(this, child);
        };
    }
    
    // Monitor for download events
    function monitorDownloads() {
        // Watch for download links being created
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) { // Element node
                        // Check if it's a download link
                        if (node.tagName === 'A' && node.download && node.href) {
                            const context = getTweetContext();
                            if (node.href.includes('pbs.twimg.com') || node.href.includes('video.twimg.com')) {
                                const newFilename = generateFilename(node.href, context);
                                console.log(`[Serial Numberer] Renaming download: ${node.download} → ${newFilename}`);
                                node.download = newFilename;
                            }
                        }
                        
                        // Check child elements
                        const downloadLinks = node.querySelectorAll && node.querySelectorAll('a[download]');
                        if (downloadLinks) {
                            downloadLinks.forEach(link => {
                                const context = getTweetContext();
                                if (link.href.includes('pbs.twimg.com') || link.href.includes('video.twimg.com')) {
                                    const newFilename = generateFilename(link.href, context);
                                    console.log(`[Serial Numberer] Renaming download: ${link.download} → ${newFilename}`);
                                    link.download = newFilename;
                                }
                            });
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // Reset counters when navigating to new tweet
    let currentUrl = location.href;
    function monitorNavigation() {
        setInterval(() => {
            if (location.href !== currentUrl) {
                const oldUrl = currentUrl;
                currentUrl = location.href;
                
                // Reset counters if we moved to a different tweet
                const oldTweetId = oldUrl.match(/\/status\/(\d+)/)?.[1];
                const newTweetId = currentUrl.match(/\/status\/(\d+)/)?.[1];
                
                if (oldTweetId !== newTweetId) {
                    console.log('[Serial Numberer] Navigation detected, resetting counters');
                    downloadCounters.clear();
                }
            }
        }, 1000);
    }
    
    // Initialize
    function init() {
        console.log('[Serial Numberer] Initializing download interceptor...');
        interceptDownloads();
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                monitorDownloads();
                monitorNavigation();
            });
        } else {
            monitorDownloads();
            monitorNavigation();
        }
    }
    
    init();
    
    // Expose function to manually reset counters
    window.resetDownloadCounters = () => {
        downloadCounters.clear();
        console.log('[Serial Numberer] Download counters reset');
    };
    
    // Expose function to change filename format
    window.setFilenameFormat = (format) => {
        FILENAME_FORMAT = format;
        console.log('[Serial Numberer] Filename format updated:', format);
    };
    
})();
