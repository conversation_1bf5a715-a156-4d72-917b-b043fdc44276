body,
#root,
html {
  width: 100%;
  height: 100%;
  font-size: 14px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root,
.btd-settings-sidebar {
  --settings-modal-background: #151f2a;
  --settings-modal-menu-item-selected: white;
  --settings-modal-menu-item-hover: white;
  --settings-modal-menu-item-hover-fg: white;
  --settings-modal-menu-item: rgba(255, 255, 255, 0.7);
  --settings-modal-text: white;
  --settings-modal-muted-text: #8899a6;
  --settings-modal-separator: rgb(0, 0, 0);
  --twitter-input-bg: #0d1118;
  --twitter-input-placeholder: #8899a6;
  --twitter-input-border-color: #14171a;
}

:root {
  --twitter-blue: #1da1f2;
  --twitter-darkblue: #005fd1;
}

@media screen and (prefers-color-scheme: light) {
  :root {
    --settings-modal-background: #ffffff;
    --settings-modal-menu-item-selected: #1da1f2;
    --settings-modal-menu-item-hover: #1170aa;
    --settings-modal-menu-item: #14171a;
    --settings-modal-text: #14171a;
    --settings-modal-separator: #ccd6dd;
    --twitter-input-bg: white;
    --twitter-input-placeholder: #8899a6;
    --twitter-input-border-color: #e1e8ed;
  }
}
.btd-settings-modal {
  overflow: hidden;
  color: var(--settings-modal-text);
  width: 100%;
  background-color: var(--settings-modal-background);
  display: grid;
  height: 100%;
  grid-template-areas:
    'sidebar   header'
    'sidebar  content'
    'sidebar   footer';
  grid-template-rows: auto 1fr auto;
  grid-template-columns: auto 1fr;
  border-radius: 0;
  position: absolute;

  font-family: system-ui;

  box-shadow: 0 1px 0 black, 0 4px 16px rgba(0, 0, 0, 0.6);
}

.btd-settings-button {
  border-radius: 100px;
  font-size: 14px;
  line-height: 21px;
  font-weight: bold;
  outline: none;
  border: 1px solid;
  padding: 3px 12px;
  cursor: pointer;
  text-decoration: none;
}

.btd-settings-button.primary {
  color: white;
  background-color: var(--twitter-blue);
  border-color: var(--twitter-blue);

  &:hover {
    background-color: var(--twitter-darkblue);
    border-color: var(--twitter-darkblue);
  }

  &[disabled],
  &.disabled {
    opacity: 0.7;
    pointer-events: none;
  }
}

.btd-settings-button.secondary {
  color: var(--twitter-blue);
  background-color: transparent;
  border-color: var(--twitter-blue);

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.btd-settings-content {
  grid-area: content;
  overflow: auto;
}

.btd-settings-content > div:last-child {
  margin-bottom: 40px;
}

.btd-settings-footer {
  grid-area: footer;
  padding: 10px;
  border-top: 1px solid var(--settings-modal-separator);

  display: flex;
  justify-content: flex-start;
  align-items: center;
  column-gap: 10px;

  transition: transform 300ms ease;
  transform: translateY(100%);

  &.visible {
    transform: translateY(0%);
  }
}

.btd-settings-footer > div {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
}

.btd-settings-footer-label {
  font-size: 14px;
  margin: 0 10px;
  color: var(--twitter-blue);
  opacity: 0.8;
}

.btd-settings-header {
  grid-area: header;
  padding: 15px;
  font-size: 20px;
  text-align: left;
  border-bottom: 1px solid var(--settings-modal-separator);

  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: auto;
  grid-column-gap: 10px;
  justify-content: flex-start;
  align-items: center;
}

.btd-settings-header .icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: -6px;
}

.btd-settings-header img {
  height: 32px;
  width: 32px;
}

.btd-settings-header .version {
  color: var(--settings-modal-muted-text);
  font-size: 14px;
}

/**
 * Sidebar
 */

.btd-settings-sidebar {
  grid-area: sidebar;
  width: 200px;
  border-right: 1px solid var(--settings-modal-separator);
  background-color: var(--settings-modal-background);
  color: var(--settings-modal-text);
  overflow: auto;
  padding-bottom: 20px;
}

.btd-settings-sidebar .section-title {
  color: var(--settings-modal-muted-text);
  font-size: 20px;
  padding: 16px;
}

.btd-settings-sidebar ul {
  list-style: none;
}

.btd-settings-sidebar ul li,
.btd-settings-sidebar ul li .text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.btd-settings-sidebar ul li a {
  text-decoration: none;
  display: inline-block;
  width: 100%;
}

.btd-settings-sidebar ul li a:any-link {
  color: currentColor;
}

.btd-settings-sidebar ul li {
  user-select: none;
  padding: 10px 16px;
  cursor: pointer;
  font-weight: bold;
  color: var(--settings-modal-menu-item);

  &.active {
    color: var(--settings-modal-menu-item-selected);
    box-shadow: inset 4px 0 0 currentColor;
  }

  &:hover {
    color: var(--settings-modal-menu-item-hover);
    box-shadow: inset 4px 0 0 currentColor;
  }
}

/* .btd-settings-sidebar ul li .icon {
  grid-area: icon;
  width: 20px;
  height: 20px;
  color: currentColor;
  background: grey;
}

.btd-settings-sidebar ul li .icon svg {
  max-width: 20px;
  max-height: 20px;
}

.btd-settings-sidebar ul li .text {
  grid-area: text;
} */
