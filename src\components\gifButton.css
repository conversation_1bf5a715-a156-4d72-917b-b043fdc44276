.btd-gif-button {
  transition-property: transform, opacity;
  transition-duration: 300ms;
  transition-timing-function: ease;
  float: left;

  &.-visible {
    opacity: 1;
    transform: translateX(0);
  }
}

.btd-gif-button {
  font-weight: bold;
  font-size: 10px;
  border-radius: 3px;
  padding: 2px 6px;
  border: 1px solid currentColor;
  cursor: pointer;
  overflow: hidden;
  position: absolute;
  left: 10px;
  bottom: 10px;
  z-index: 0;
  opacity: 0;
  transition-property: color, opacity;
  user-select: none;
  pointer-events: none;

  &:hover {
    color: #616161 !important;
  }
}

.btd-gif-button.-visible {
  opacity: 1;
  pointer-events: auto;
}

.btd-gif-button.btd-gif-button:hover {
  color: #616161;
}
