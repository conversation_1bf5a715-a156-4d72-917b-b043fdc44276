{"app_desc": {"message": "Take TweetDeck to the next level!"}, "app_name": {"message": "Better TweetDeck"}, "settings_title": {"message": "Better TweetDeck"}, "settings_show_cards_inside_columns": {"message": "Show tweet cards inside columns"}, "settings_show_profile_badges_on_top_of_avatars": {"message": "Show profile badges on top of avatars"}, "settings_collapse_read_dms": {"message": "Collapse read DMs"}, "settings_freeze_gifs_in_profile_pictures": {"message": "Freeze GIFs in profile pictures"}, "settings_remove_t_co_redirection_on_links": {"message": "Remove t.co redirection on links"}, "settings_make_buttons_smaller_in_the_composer": {"message": "Make buttons smaller in the composer"}, "settings_reflect_new_tweets_and_dms_in_the_tabs_title": {"message": "Reflect new tweets and DMs in the tab's title"}, "settings_auto_switch_light_theme": {"message": "Switch to light theme when OS is in light mode"}, "settings_scrollbar_default": {"message": "<PERSON><PERSON><PERSON>"}, "settings_scrollbar_thin": {"message": "Thin"}, "settings_scrollbar_hidden": {"message": "Hidden"}, "settings_style_of_scrollbars": {"message": "Style of scrollbars"}, "settings_show_clear_button_column": {"message": "Show \"Clear\" button in columns' header"}, "settings_show_collapse_button_in_columns_header": {"message": "Show \"Collapse\" button in columns' header"}, "settings_hide_icons_on_top_of_columns": {"message": "Hide icons on top of columns"}, "settings_use_old_style_of_replies": {"message": "Use old style of replies (inline @mentions)"}, "settings_timestamp_relative": {"message": "Relative"}, "settings_timestamp_custom": {"message": "Custom"}, "settings_date_format": {"message": "Date format"}, "settings_short_time_after_24h": {"message": "Use a different date format after 24h"}, "settings_timestamp_presets": {"message": "Presets"}, "settings_timestamp_preset_absolute": {"message": "Absolute"}, "settings_timestamp_preset_absolute_us": {"message": "Absolute (U.S. style)"}, "settings_fullname_username": {"message": "Fullname and username"}, "settings_username_fullname": {"message": "Username and fullname"}, "settings_username": {"message": "Username only"}, "settings_fullname": {"message": "Fullname only"}, "settings_name_display_style": {"message": "Name display style"}, "settings_general": {"message": "General"}, "settings_theme": {"message": "Theme"}, "settings_tweets_display": {"message": "Tweets display"}, "settings_tweet_actions": {"message": "Tweet actions"}, "settings_actions_visibility_always": {"message": "Always"}, "settings_actions_visibility_on_hover": {"message": "On hover"}, "settings_actions_visibility": {"message": "Actions visibility"}, "settings_position_of_actions": {"message": "Position of actions"}, "settings_actions_position_left": {"message": "Left"}, "settings_actions_position_right": {"message": "Right"}, "settings_action_block_author": {"message": "Block author"}, "settings_action_mute_author": {"message": "Mute author"}, "settings_action_copy_media_links": {"message": "Copy media links"}, "settings_action_download_media": {"message": "Download media"}, "settings_additional_actions": {"message": "Tweet actions to add"}, "settings_downloaded_filename_format": {"message": "Downloaded filename format"}, "settings_filename_format_tokens": {"message": "Filename format tokens"}, "settings_token_username_without": {"message": "username (without @)"}, "settings_token_tweet_id": {"message": "Tweet ID"}, "settings_token_filename": {"message": "Filename"}, "settings_token_file_extension": {"message": "File extension"}, "settings_token_year": {"message": "Year"}, "settings_token_day": {"message": "Day"}, "settings_token_month": {"message": "Month"}, "settings_token_minutes": {"message": "Minutes"}, "settings_token_seconds": {"message": "Seconds"}, "settings_token_serial": {"message": "Serial number"}, "settings_menu_item_mute_hashtags": {"message": "Mute #hashtags"}, "settings_menu_item_mute_source": {"message": "Mute tweet's source"}, "settings_menu_item_redraft": {"message": "Re-draft"}, "settings_additional_tweet_menu_items": {"message": "Additional tweet menu items"}, "settings_replace_hearts_by_stars": {"message": "Replace hearts by stars"}, "settings_custom_css": {"message": "Custom CSS"}, "settings_custom_css_warning": {"message": "Pasting unknown code in this editor can lead to weird issues if you don't know what you are doing"}, "settings_avatar_shape": {"message": "Avatars shape"}, "settings_save": {"message": "Save"}, "settings_use_original_aspect_ratio_images": {"message": "Display single images with their original aspect ratio (only for Medium and Large sizes)"}, "settings_show_delete_button_in_columns_header": {"message": "Show \"Delete\" button in columns' header"}, "settings_section_settings": {"message": "Settings"}, "settings_support": {"message": "Support"}, "settings_avatar_square": {"message": "Square"}, "settings_avatar_circle": {"message": "Circle"}, "settings_display_modern_fullscreen_images": {"message": "Display fullscreen images à la Twitter Web"}, "settings_browser_and_extension_informations": {"message": "Browser and extension informations"}, "settings_version": {"message": "Version:"}, "settings_user_agent": {"message": "User agent:"}, "settings_export_settings": {"message": "Export settings"}, "settings_export_settings_copy": {"message": "Export your settings by clicking the button below"}, "settings_download_settings_button": {"message": "Download settings"}, "settings_import_settings": {"message": "Import settings"}, "settings_import_settings_copy": {"message": "Import your settings from a JSON file"}, "settings_import_json_wrong_keys": {"message": "Your file does not match the format of Better TweetDeck's settings"}, "settings_import_success": {"message": "Your settings have correctly been imported, don't forget to hit the Save button!"}, "settings_imported_settings_summary": {"message": "Click here to view imported settings"}, "settings_website": {"message": "Website"}, "settings_links": {"message": "Links"}, "settings_footer_label": {"message": "TweetDeck will need to be reloaded for changes to take effect!"}, "settings_show_account_picker_like": {"message": "Show account picker when clicking on the like button"}, "settings_show_account_picker_follow": {"message": "Show account picker when clicking on the follow button"}, "settings_accent_color": {"message": "Accent color"}, "settings_misc": {"message": "Misc."}, "settings_meta": {"message": "Meta"}, "settings_import_export": {"message": "Import / Export"}, "settings_columns": {"message": "Columns"}, "settings_tweet_content": {"message": "Tweet content"}, "settings_pause_column_scrolling_on_hover": {"message": "Pause column scrolling on hover"}, "settings_use_a_custom_width_for_columns": {"message": "Use a custom width for columns"}, "settings_width_any_valid_css_value": {"message": "Width (any valid CSS value)"}, "settings_share_on_tweetdeck": {"message": "Share on TweetDeck"}, "settings_enable_share_item": {"message": "Add a \"Share on TweetDeck\" item in the browser's contextual menu"}, "settings_shorten_the_shared_text": {"message": "Shorten the shared text"}, "settings_contextual_menu": {"message": "Contextual menu"}, "settings_old_gray": {"message": "Old Gray"}, "settings_super_black": {"message": "Super Black"}, "settings_tokens_list": {"message": "Tokens list"}, "settings_make_emoji_bigger_in_tweets": {"message": "Make emoji bigger in tweets"}, "settings_add_search_columns_first_in_the_list": {"message": "Add search columns first in the list"}, "settings_save_tweeted_hashtags": {"message": "Save tweeted hashtags"}, "settings_changelog": {"message": "Changelog"}, "settings_bugs_or_suggestions": {"message": "Bugs or suggestions"}, "settings_source_on_github": {"message": "Source on GitHub"}, "settings_contributors": {"message": "Contributors"}, "settings_author": {"message": "Author"}, "settings_credits_about": {"message": "Credits / About"}, "settings_always_characters_left": {"message": "Always show the number of characters left in the tweet composer"}, "settings_better_tweetdeck_ask_tabs": {"message": "Better TweetDeck will ask access to browser tabs for this to work properly"}, "settings_show_like_rt_indicators_on_top_of_tweets": {"message": "Show like/RT indicators on top of tweets"}, "settings_do_the_same_for_single_images_in_quoted_tweets": {"message": "Do the same for single images in quoted tweets"}, "settings_default_dark_theme": {"message": "<PERSON><PERSON><PERSON>"}, "settings_custom_dark_theme": {"message": "Dark theme"}, "settings_tweet_composer": {"message": "Tweet composer"}, "settings_show_the_emoji_picker": {"message": "Show the emoji picker"}, "settings_enable_emoji_autocompletion": {"message": "Enable emoji autocompletion using :shortcodes:"}, "settings_enable_the_gif_button": {"message": "Enable the GIF button"}, "settings_also_show_cards_in_columns_with_small_media_size": {"message": "Also show cards in columns with Small media size"}, "settings_looking_for_inspiration": {"message": "Looking for inspiration?"}, "settings_check_the_collection_of_css_snippets": {"message": "Check the collection of CSS snippets"}, "settings_css_compress_warning": {"message": "The CSS in the editor will be compressed in order to save space towards your browser's\n        storage quota. Meaning some indentation or white space will be removed."}, "settings_backup_warning": {"message": "Don't forget to keep backups of your work in another place!"}, "settings_usernames_like_picker_allowlist": {"message": "Comma separated list of usernames for which the picker should show (leave empty to always show)"}, "settings_override_translation_language": {"message": "Use a specific language when translating tweets"}, "settings_default_browsers_language": {"message": "Default (browser's language)"}, "settings_logo_variation": {"message": "Logo variation"}, "settings_default": {"message": "<PERSON><PERSON><PERSON>"}, "settings_action_follow_author": {"message": "Add action to follow author"}, "settings_follow_actions": {"message": "Follow action"}, "settings_show_followers_count": {"message": "Show the number of followers next to the follow icon"}, "settings_show_verified_badges": {"message": "Show verified badges"}, "settings_show_translator_badges": {"message": "Show translator badges"}, "settings_show_badges_on_mutuals": {"message": "Show badges on mutuals (you follow them and they follow you)"}, "settings_badges": {"message": "Badges"}, "settings_show_a_clear_all_columns_button_in_the_sidebar": {"message": "Show \"Clear all columns\" button in the sidebar"}, "settings_require_alt_images": {"message": "Disable the tweet button until all images have a description"}, "settings_content_warning_hint": {"message": "We'll check tweets for containing commonly used cw/tw/cn warnings to display these!"}, "settings_show_content_warnings": {"message": "Show content warnings for appropriately marked tweets"}, "settings_images": {"message": "Images"}, "settings_collapse_unread_dms": {"message": "Also collapse unread DMs"}, "settings_mutual_badge_use_a_heart": {"message": "Use a heart"}, "settings_mutual_badge_use_double_arrows": {"message": "Use double arrows"}, "settings_show_account_avatars_on_top_of_columns": {"message": "Show account avatars on top of columns"}, "settings_show_conversation_control_button": {"message": "Show conversation control button"}, "settings_disable_it_in_the_dm_composer_too": {"message": "Applies to the DM composer too"}, "settings_show_profile_labels_in_tweets_and_profile_modals": {"message": "Show \"profile labels\" in tweets and profile modals"}, "settings_hide_the_try_tweetdeck_preview_button": {"message": "Hide the \"Try TweetDeck Preview\" button"}, "settings_pronouns_extra": {"message": "Only support English pronouns for now. If you notice any errors, please let me know on @BetterTDeck"}, "settings_extract_pronouns": {"message": "Extract pronouns from users' biography or location and show them within columns"}, "settings_mute_nfts_accounts": {"message": "Mute accounts who use the NFT avatar integration (hexagon-shaped avatar)"}, "settings_require_confirmation_for_block_and_mute_actions": {"message": "Require confirmation for block and mute actions"}, "settings_better_tweetdeck_has_been_updated": {"message": "Better TweetDeck has been updated!"}, "settings_click_this_notification_to_reload": {"message": "Click this notification to reload your TweetDeck tab(s) to grab the newest update!"}, "settings_show_twitters_warnings_on_media": {"message": "Show Twitter's warnings on media"}, "settings_twitter_added_feature_in_january_2022": {"message": "Twitter added feature in January 2022"}, "settings_show_warnings_for_adult_content": {"message": "Show warnings for media with nudity"}, "settings_show_warnings_for_graphic_violence": {"message": "Show warnings for media with graphic violence"}, "settings_show_warnings_for_sensitive_contents": {"message": "Show warnings for media with sensitive content"}, "settings_warnings": {"message": "Warnings"}, "settings_detect_content_warnings_without_the_keyword": {"message": "Detect content warnings without the CW/TW keyword(s)"}, "settings_collapse_tweets_who_match_one_of_the_following_keywords": {"message": "Collapse tweets who match one of the following keywords:"}, "settings_comma_separated_keywords_matched_by_order_in_the_list": {"message": "Comma separated keywords, matched by order in the list"}, "settings_will_match_patterns_like_food_lorem_ipsum": {"message": "Will match patterns like \"[food] lorem ipsum\""}, "settings_you_can_use_this_to_hide_spoilers": {"message": "You can use this to hide spoilers"}, "settings_require_a_confirmation_before_deleting_and_editing": {"message": "Require a confirmation before deleting and editing"}, "settings_force_update_banners_to_be_dismissed": {"message": "Force update banners to be dismissed"}, "settings_force_dismissed_banner": {"message": "Force dismissed banner"}, "settings_dismiss_banner_paragraph": {"message": "In some cases, the banner that shows up after a Better TweetDeck update can have trouble dismissing itself. Click the button below to fix it."}, "settings_dont_show_pronouns_for_your_own_accounts": {"message": "Don't show pronouns for your own accounts"}, "settings_use_a_verified_icon": {"message": "Use a \"verified\" icon"}, "settings_use_the_twitter_blue_icon": {"message": "Use the Twitter Blue icon"}, "settings_use_a_dollar_icon": {"message": "Use a dollar icon"}, "settings_use_a_clown_icon": {"message": "Use a clown icon"}, "settings_use_a_nerd_icon": {"message": "Use a nerd icon"}, "settings_mute_circle_tweets": {"message": "Mute tweets created for Twitter Circles"}, "settings_show_circle_tweets_border": {"message": "Show green border around profile pictures of <PERSON> Tweets"}}